import React, { useState, useEffect } from 'react';
import { MetricsCards } from './MetricsCards';
import { QRCodesTable } from './QRCodesTable';
import { TablePagination } from './TablePagination';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import {
  Plus,
  Settings,
  Download,
  Upload,
  RefreshCw,
  Zap
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '../ui/dropdown-menu';
import type {
  QRCode,
  PaginationInfo,
  DashboardMetrics,
  DashboardMetricsResponse,
  QRCodesListResponse
} from '../../types/dashboard';

export const DashboardContent: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [qrCodes, setQrCodes] = useState<QRCode[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10
  });
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFAB, setShowFAB] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState({
    isDownloading: false,
    step: "",
    progress: 0
  });



  // Show/hide FAB based on scroll
  useEffect(() => {
    const handleScroll = () => {
      setShowFAB(window.scrollY > 200);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Fetch dashboard metrics
  const fetchMetrics = async () => {
    try {
      console.log('Fetching dashboard metrics...');

      // Get user's timezone
      const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      console.log('User timezone:', userTimezone);

      const response = await fetch(`/api/dashboard/metrics?timezone=${encodeURIComponent(userTimezone)}`, {
        credentials: 'include', // Include cookies
        headers: {
          'Content-Type': 'application/json',
        }
      });

      console.log('Metrics response status:', response.status);
      const result: DashboardMetricsResponse = await response.json();
      console.log('Metrics response:', result);

      if (result.success && result.data) {
        setMetrics(result.data);
      } else {
        console.error('Metrics fetch failed:', result.error);
        setError(result.error || 'Failed to fetch metrics');
      }
    } catch (err) {
      console.error('Error fetching metrics:', err);
      setError('Failed to connect to server. Please check your connection.');
    }
  };

  // Fetch QR codes data
  const fetchQRCodes = async () => {
    try {
      console.log('Fetching QR codes...');
      const response = await fetch(`/api/dashboard/qr-codes?page=${currentPage}&limit=${pageSize}`, {
        credentials: 'include', // Include cookies
        headers: {
          'Content-Type': 'application/json',
        }
      });

      console.log('QR codes response status:', response.status);
      const result: QRCodesListResponse = await response.json();
      console.log('QR codes response:', result);

      if (result.success && result.data) {
        setQrCodes(result.data.qrCodes);
        setPagination(result.data.pagination);
      } else {
        console.error('QR codes fetch failed:', result.error);
        setError(result.error || 'Failed to fetch QR codes');
      }
    } catch (err) {
      console.error('Error fetching QR codes:', err);
      setError('Failed to connect to server. Please check your connection.');
    }
  };

  // Load data on component mount and when pagination changes
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setError(null);

      await Promise.all([
        fetchMetrics(),
        fetchQRCodes()
      ]);

      setLoading(false);
    };

    loadData();
  }, [currentPage, pageSize]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  const handleViewDetails = (qrCodeId: string) => {
    // Navigate to QR analytics page
    window.location.href = `/dashboard/qr-analytics/${qrCodeId}`;
  };

  const handleDownloadQR = async (qrCodeId: string) => {
    try {
      console.log('Downloading QR code:', qrCodeId);

      // Find the QR code data
      const qrCode = qrCodes.find(qr => qr.id === qrCodeId);
      if (!qrCode) {
        alert('QR code not found');
        return;
      }

      // Set up progress tracking
      setDownloadProgress({ isDownloading: true, step: "Fetching QR data...", progress: 5 });

      // Fetch QR code options from database
      const response = await fetch(`/api/qr-codes/${qrCodeId}/download`);
      if (!response.ok) {
        throw new Error('Failed to fetch QR code data');
      }

      const result = await response.json() as { success: boolean; data?: any; error?: string };
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch QR code data');
      }

      // Use the enhanced download utility
      const { downloadQRCodeFromDatabase } = await import('../../lib/qr-download');

      await downloadQRCodeFromDatabase(
        result.data,
        (progress) => setDownloadProgress(progress)
      );

      console.log('QR code downloaded successfully using stored options');
    } catch (error) {
      console.error('Download error:', error);
      setDownloadProgress({ isDownloading: false, step: "", progress: 0 });
    }
  };

  // Quick action handlers
  const handleCreateNewQR = () => {
    window.location.href = '/tool/qr-code-generator';
  };

  const handleRefreshData = async () => {
    setLoading(true);
    setError(null);
    try {
      await Promise.all([fetchMetrics(), fetchQRCodes()]);
      // Show success message
      console.log('Data refreshed successfully');
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setLoading(false);
    }
  };



  const handleBulkDownload = async () => {
    try {
      console.log('Starting bulk download...');
      // Create a zip file with all QR codes
      const selectedQRs = qrCodes.length > 0 ? qrCodes : [];
      if (selectedQRs.length === 0) {
        alert('No QR codes available to download');
        return;
      }

      setDownloadProgress({ isDownloading: true, step: "Starting bulk download...", progress: 0 });

      // For now, download each QR code individually
      // In a real implementation, you'd create a zip file
      const qrsToDownload = selectedQRs.slice(0, 5); // Limit to first 5 for demo

      for (let i = 0; i < qrsToDownload.length; i++) {
        const qr = qrsToDownload[i];
        const progress = Math.round(((i + 1) / qrsToDownload.length) * 100);

        setDownloadProgress({
          isDownloading: true,
          step: `Downloading ${qr.name || 'QR Code'} (${i + 1}/${qrsToDownload.length})...`,
          progress
        });

        await handleDownloadQR(qr.id);
        await new Promise(resolve => setTimeout(resolve, 1000)); // Delay between downloads
      }

      setDownloadProgress({ isDownloading: true, step: "Complete!", progress: 100 });

      setTimeout(() => {
        setDownloadProgress({ isDownloading: false, step: "", progress: 0 });
        alert(`Downloaded ${qrsToDownload.length} QR codes`);
      }, 1500);

    } catch (error) {
      console.error('Bulk download error:', error);
      setDownloadProgress({ isDownloading: false, step: "", progress: 0 });
      alert('Error during bulk download');
    }
  };

  const handleImportQRs = () => {
    // Create file input for importing QR codes
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.csv,.json';
    input.multiple = false;

    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      try {
        const text = await file.text();
        console.log('Import file content:', text);
        // Here you would parse the file and create QR codes
        alert('Import functionality would be implemented here');
      } catch (error) {
        console.error('Import error:', error);
        alert('Error importing file');
      }
    };

    input.click();
  };

  // Enhanced Loading state with sophisticated skeletons
  if (loading) {
    return (
      <div className="space-y-8 animate-in fade-in-0 duration-500">
        {/* Header skeleton */}
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="h-8 w-8 bg-gradient-to-r from-gray-200 to-gray-300 rounded-full animate-pulse"></div>
            <div className="h-8 w-64 bg-gradient-to-r from-gray-200 to-gray-300 rounded-lg animate-pulse"></div>
          </div>
          <div className="h-4 w-96 bg-gradient-to-r from-gray-200 to-gray-300 rounded animate-pulse"></div>
        </div>

        {/* Metrics cards skeleton */}
        <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="relative overflow-hidden bg-white rounded-xl border border-gray-200 shadow-lg">
              <div className="p-6 space-y-4">
                <div className="flex items-center justify-between">
                  <div className="h-4 w-32 bg-gradient-to-r from-gray-200 to-gray-300 rounded animate-pulse"></div>
                  <div className="h-12 w-12 bg-gradient-to-r from-blue-200 to-blue-300 rounded-xl animate-pulse"></div>
                </div>
                <div className="h-8 w-20 bg-gradient-to-r from-gray-300 to-gray-400 rounded animate-pulse"></div>
                <div className="flex items-center space-x-2">
                  <div className="h-6 w-16 bg-gradient-to-r from-green-200 to-green-300 rounded-full animate-pulse"></div>
                  <div className="h-3 w-24 bg-gradient-to-r from-gray-200 to-gray-300 rounded animate-pulse"></div>
                </div>
                <div className="h-3 w-full bg-gradient-to-r from-gray-200 to-gray-300 rounded animate-pulse"></div>
              </div>
              {/* Shimmer effect */}
              <div className="absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
            </div>
          ))}
        </div>

        {/* Table skeleton */}
        <div className="bg-white rounded-xl border border-gray-200 shadow-lg overflow-hidden">
          <div className="p-6 border-b border-gray-100 bg-gray-50/50">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <div className="h-5 w-5 bg-gradient-to-r from-blue-200 to-blue-300 rounded animate-pulse"></div>
                <div className="h-6 w-32 bg-gradient-to-r from-gray-200 to-gray-300 rounded animate-pulse"></div>
              </div>
            </div>
            <div className="flex space-x-3">
              <div className="h-10 flex-1 bg-gradient-to-r from-gray-200 to-gray-300 rounded-lg animate-pulse"></div>
              <div className="h-10 w-32 bg-gradient-to-r from-gray-200 to-gray-300 rounded-lg animate-pulse"></div>
              <div className="h-10 w-32 bg-gradient-to-r from-gray-200 to-gray-300 rounded-lg animate-pulse"></div>
            </div>
          </div>
          <div className="p-6 space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-center space-x-4 p-4 rounded-lg bg-gray-50/50">
                <div className="h-4 w-4 bg-gradient-to-r from-gray-200 to-gray-300 rounded animate-pulse"></div>
                <div className="h-10 w-10 bg-gradient-to-r from-blue-200 to-blue-300 rounded-lg animate-pulse"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 w-48 bg-gradient-to-r from-gray-200 to-gray-300 rounded animate-pulse"></div>
                  <div className="h-3 w-64 bg-gradient-to-r from-gray-200 to-gray-300 rounded animate-pulse"></div>
                </div>
                <div className="h-4 w-24 bg-gradient-to-r from-gray-200 to-gray-300 rounded animate-pulse"></div>
                <div className="h-6 w-16 bg-gradient-to-r from-green-200 to-green-300 rounded-full animate-pulse"></div>
                <div className="flex space-x-1">
                  <div className="h-8 w-8 bg-gradient-to-r from-gray-200 to-gray-300 rounded animate-pulse"></div>
                  <div className="h-8 w-8 bg-gradient-to-r from-gray-200 to-gray-300 rounded animate-pulse"></div>
                  <div className="h-8 w-8 bg-gradient-to-r from-gray-200 to-gray-300 rounded animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Enhanced Error state with better visual feedback
  if (error) {
    return (
      <div className="min-h-[60vh] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          {/* Error illustration */}
          <div className="w-24 h-24 mx-auto mb-6 bg-red-100 rounded-full flex items-center justify-center">
            <svg className="w-12 h-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>

          <h2 className="text-2xl font-bold text-gray-900 mb-3">Oops! Something went wrong</h2>
          <p className="text-gray-600 mb-6 leading-relaxed">
            {error === 'Failed to fetch metrics'
              ? "We're having trouble loading your dashboard data. This might be a temporary issue."
              : error === 'Failed to fetch QR codes'
              ? "We couldn't load your QR codes right now. Please check your connection and try again."
              : error
            }
          </p>

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg font-medium hover:from-blue-700 hover:to-blue-800 transition-all duration-200 transform hover:scale-105 shadow-lg"
            >
              <svg className="w-4 h-4 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Try Again
            </button>
            <button
              onClick={() => window.location.href = '/dashboard/support'}
              className="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors"
            >
              Contact Support
            </button>
          </div>

          {/* Additional help */}
          <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="text-sm font-semibold text-blue-900 mb-2">Need help?</h3>
            <p className="text-xs text-blue-700">
              If this problem persists, please contact our support team with the error details above.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 relative">
      {/* Dashboard Metrics */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-3xl font-bold tracking-tight text-gray-900">Dashboard Overview</h2>
            <p className="text-gray-600 mt-1">Monitor your QR code performance and analytics</p>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefreshData}
              className="flex items-center space-x-1"
            >
              <RefreshCw className="h-4 w-4" />
              <span className="hidden sm:inline">Refresh</span>
            </Button>
          </div>
        </div>

        {metrics ? (
          <MetricsCards metrics={metrics} />
        ) : !loading && !error ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <Zap className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Welcome to QRAnalytica!</h3>
            <p className="text-gray-600 mb-4">Start by creating your first QR code to see metrics here.</p>
            <Button
              onClick={handleCreateNewQR}
              className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Your First QR Code
            </Button>
          </div>
        ) : null}
      </div>

      {/* QR Codes Section */}
      <div>
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">QR Codes</h2>
            <p className="text-gray-600 text-sm mt-1">Manage and track your QR codes</p>
          </div>
          <div className="flex items-center space-x-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-1" />
                  <span className="hidden sm:inline">Bulk Actions</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleBulkDownload} className="cursor-pointer">
                  <Download className="h-4 w-4 mr-2" />
                  Download All
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleImportQRs} className="cursor-pointer">
                  <Upload className="h-4 w-4 mr-2" />
                  Import QR Codes
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="cursor-pointer">
                  <Settings className="h-4 w-4 mr-2" />
                  Export Settings
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button
              onClick={handleCreateNewQR}
              className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg"
            >
              <Plus className="h-4 w-4 mr-1" />
              Create New QR Code
            </Button>
          </div>
        </div>

        <QRCodesTable
          qrCodes={qrCodes}
          onViewDetails={handleViewDetails}
          onDownloadQR={handleDownloadQR}
        />

        {/* Download Progress */}
        {downloadProgress.isDownloading && (
          <div className="mt-4 p-4 bg-slate-50 rounded-lg border">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-slate-700">
                {downloadProgress.step}
              </span>
              <span className="text-sm text-slate-500">
                {downloadProgress.progress}%
              </span>
            </div>
            <Progress value={downloadProgress.progress} className="h-2" />
          </div>
        )}

        <TablePagination
          pagination={pagination}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
        />
      </div>

      {/* Floating Action Button */}
      {showFAB && (
        <div className="fixed bottom-6 right-6 z-50">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                size="lg"
                className="h-14 w-14 rounded-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-110"
              >
                <Plus className="h-6 w-6" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={handleCreateNewQR} className="cursor-pointer">
                <Plus className="h-4 w-4 mr-2" />
                Create QR Code
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleImportQRs} className="cursor-pointer">
                <Upload className="h-4 w-4 mr-2" />
                Import QR Codes
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleBulkDownload} className="cursor-pointer">
                <Download className="h-4 w-4 mr-2" />
                Bulk Download
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}

      {/* Keyboard Shortcuts Modal */}
      {showKeyboardShortcuts && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-2xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-900">Keyboard Shortcuts</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowKeyboardShortcuts(false)}
                className="h-8 w-8 p-0"
              >
                ×
              </Button>
            </div>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Create new QR code</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-xs font-mono">Ctrl + N</kbd>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Refresh data</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-xs font-mono">Ctrl + R</kbd>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Focus search</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-xs font-mono">Ctrl + K</kbd>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Show shortcuts</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-xs font-mono">Ctrl + /</kbd>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Close modal</span>
                <kbd className="px-2 py-1 bg-gray-100 rounded text-xs font-mono">Esc</kbd>
              </div>
            </div>
            <div className="mt-6 pt-4 border-t border-gray-200">
              <p className="text-xs text-gray-500 text-center">
                Press <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs font-mono">?</kbd> for help
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DashboardContent;
